package com.xiang.config;

import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.server.authorization.config.annotation.web.configurers.OAuth2AuthorizationServerConfigurer;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.util.matcher.RequestMatcher;

@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

        private final PasswordGrantAuthenticationProvider passwordGrantAuthenticationProvider;
        private final JwtDecoder jwtDecoder;

        @Bean
        @Order(1)
        public SecurityFilterChain authorizationServerSecurityFilterChain(HttpSecurity http) throws Exception {
                OAuth2AuthorizationServerConfigurer authorizationServerConfigurer = new OAuth2AuthorizationServerConfigurer();

                RequestMatcher endpointsMatcher = authorizationServerConfigurer.getEndpointsMatcher();

                http
                                .securityMatcher(endpointsMatcher)
                                .authorizeHttpRequests(authorize -> authorize.anyRequest().authenticated())
                                .csrf(csrf -> csrf.ignoringRequestMatchers(endpointsMatcher))
                                .apply(authorizationServerConfigurer)
                                .tokenEndpoint(tokenEndpoint -> tokenEndpoint
                                                .accessTokenRequestConverter(new PasswordGrantAuthenticationConverter())
                                                .authenticationProvider(passwordGrantAuthenticationProvider));

                return http.build();
        }

        @Bean
        @Order(2)
        public SecurityFilterChain defaultSecurityFilterChain(HttpSecurity http) throws Exception {
                http
                                .authorizeHttpRequests(authorize -> authorize
                                                .requestMatchers("/test/**", "/api/jwt/**", "/api/register").permitAll()
                                                .anyRequest().authenticated())
                                .oauth2ResourceServer(oauth2 -> oauth2
                                                .jwt(jwt -> jwt.decoder(jwtDecoder)))
                                .csrf(csrf -> csrf.disable());

                return http.build();
        }

}