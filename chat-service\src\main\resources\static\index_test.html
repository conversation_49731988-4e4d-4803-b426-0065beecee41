<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天微服务测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }

        .main-content {
            display: flex;
            height: 600px;
            width: 100%;
        }

        .sidebar {
            width: 300px;
            min-width: 300px;
            max-width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            padding: 20px;
            overflow-y: auto;
            flex-shrink: 0;
        }

        .chat-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-width: 0;
        }

        .auth-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .status-section {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .room-section {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .message-input {
            padding: 20px;
            background: white;
            border-top: 1px solid #e9ecef;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #495057;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 14px;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 5px;
        }

        .btn-primary {
            background: #007bff;
            color: white;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-warning {
            background: #ffc107;
            color: #212529;
        }

        .btn:hover {
            opacity: 0.8;
        }

        .message {
            background: white;
            border-radius: 8px;
            padding: 10px;
            margin-bottom: 10px;
            border-left: 4px solid #007bff;
        }

        .message.sent {
            border-left-color: #28a745;
            margin-left: 20px;
        }

        .message.received {
            border-left-color: #6c757d;
        }

        .message.system {
            border-left-color: #ffc107;
            background: #fff3cd;
        }

        .message.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .message-header {
            font-size: 12px;
            color: #6c757d;
            margin-bottom: 5px;
        }

        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }

        .status-online {
            background: #28a745;
        }

        .status-offline {
            background: #dc3545;
        }

        .room-list {
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 10px;
            background: white;
        }

        .room-item {
            padding: 5px;
            cursor: pointer;
            border-radius: 4px;
        }

        .room-item:hover {
            background: #e9ecef;
        }

        .room-item.active {
            background: #007bff;
            color: white;
        }

        .input-group {
            display: flex;
            gap: 10px;
        }

        .input-group .form-control {
            flex: 1;
        }

        .log-area {
            height: 150px;
            overflow-y: auto;
            background: #f8f9fa;
            border: 1px solid #ced4da;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
        }

        .message-status {
            font-size: 11px;
            margin-left: 10px;
            padding: 2px 6px;
            border-radius: 3px;
            background: #f8f9fa;
        }

        .message-status[data-status="sending"] {
            color: #6c757d;
            background: #e9ecef;
        }

        .message-status[data-status="sent"] {
            color: #28a745;
            background: #d4edda;
        }

        .message-status[data-status="delivered"] {
            color: #007bff;
            background: #d1ecf1;
        }

        .message-status[data-status="failed"] {
            color: #dc3545;
            background: #f8d7da;
        }

        /* 引用回复相关样式 */
        .reply-preview {
            background: #e9ecef;
            border-left: 3px solid #6f42c1;
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 4px;
            font-size: 13px;
            display: none;
        }

        .reply-preview .close-btn {
            float: right;
            cursor: pointer;
            color: #dc3545;
            font-weight: bold;
        }

        .reply-preview .sender {
            font-weight: bold;
            color: #6f42c1;
        }

        .reply-preview .content {
            color: #6c757d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message.reply {
            border-left-color: #6f42c1;
            background: #f8f9ff;
        }

        .reply-reference {
            background: #e9ecef;
            border-left: 3px solid #6f42c1;
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 4px;
            font-size: 13px;
            cursor: pointer;
        }

        .reply-reference .sender {
            font-weight: bold;
            color: #6f42c1;
        }

        .reply-reference .content {
            color: #6c757d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message-actions {
            margin-top: 8px;
        }

        .message-actions .btn {
            padding: 2px 8px;
            font-size: 12px;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Spring Cloud 聊天微服务测试平台</h1>
            <p>基于 Netty WebSocket + Spring Boot 3.x 的企业级聊天系统</p>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <!-- 认证区域 -->
                <div class="auth-section">
                    <h3>🔐 用户认证</h3>
                    <div class="form-group">
                        <label>用户ID:</label>
                        <input type="number" id="userId" class="form-control" value="1001" placeholder="输入用户ID">
                    </div>
                    <div class="form-group">
                        <label>认证Token:</label>
                        <input type="text" id="authToken" class="form-control" value="user_1001" placeholder="输入认证令牌">
                    </div>
                    <button class="btn btn-primary" onclick="connect()">连接WebSocket</button>
                    <button class="btn btn-danger" onclick="disconnect()">断开连接</button>
                </div>

                <!-- 连接状态 -->
                <div class="status-section">
                    <h3>📡 连接状态</h3>
                    <p><span class="status-indicator status-offline" id="statusIndicator"></span><span
                            id="connectionStatus">未连接</span></p>
                    <p>用户ID: <span id="currentUserId">-</span></p>
                    <p>会话ID: <span id="sessionId">-</span></p>
                    <button class="btn btn-warning" onclick="sendHeartbeat()">发送心跳</button>
                </div>

                <!-- 房间管理 -->
                <div class="room-section">
                    <h3>🏠 房间管理</h3>
                    <div class="form-group">
                        <label>房间ID:</label>
                        <input type="text" id="roomId" class="form-control" value="room_001" placeholder="输入房间ID">
                    </div>
                    <button class="btn btn-success" onclick="joinRoom()">加入房间</button>
                    <button class="btn btn-danger" onclick="leaveRoom()">离开房间</button>
                    <button class="btn btn-primary" onclick="loadPublicRooms()">加载公开房间</button>

                    <div class="form-group" style="margin-top: 15px;">
                        <label>公开房间列表:</label>
                        <div class="room-list" id="roomList">
                            <div class="room-item" onclick="selectRoom('room_001')">room_001 - 测试房间</div>
                            <div class="room-item" onclick="selectRoom('room_002')">room_002 - 开发讨论</div>
                        </div>
                    </div>
                </div>

                <!-- API测试 -->
                <div class="form-group">
                    <h3>🔧 API测试</h3>
                    <button class="btn btn-primary" onclick="getOnlineUsers()">获取在线用户</button>
                    <button class="btn btn-primary" onclick="getUserStatus()">获取用户状态</button>
                    <button class="btn btn-primary" onclick="createTestRoom()">创建测试房间</button>
                </div>
            </div>

            <div class="chat-area">
                <!-- 消息显示区域 -->
                <div class="messages" id="messages">
                    <div class="message system">
                        <div class="message-header">系统消息 - 欢迎使用聊天测试平台</div>
                        <div>请先进行用户认证，然后加入房间开始聊天</div>
                    </div>
                </div>

                <!-- 消息输入区域 -->
                <div class="message-input">
                    <!-- 回复预览区域 -->
                    <div class="reply-preview" id="replyPreview">
                        <span class="close-btn" onclick="cancelReply()">×</span>
                        <div>回复 <span class="sender" id="replySender"></span>:</div>
                        <div class="content" id="replyContent"></div>
                        <input type="hidden" id="referenceMessageId">
                        <input type="hidden" id="referenceSenderId">
                    </div>

                    <div class="form-group">
                        <label>消息类型:</label>
                        <select id="messageType" class="form-control">
                            <option value="text">文本消息</option>
                            <option value="image">图片消息</option>
                            <option value="file">文件消息</option>
                            <option value="system">系统消息</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label>接收者ID (私聊，留空为群聊):</label>
                        <input type="number" id="receiverId" class="form-control" placeholder="私聊时填写对方用户ID">
                    </div>

                    <!-- 文件上传区域 -->
                    <div class="form-group" id="fileUploadSection" style="display: none;">
                        <label>选择文件:</label>
                        <input type="file" id="fileInput" class="form-control" accept="*/*">
                        <small class="text-muted">支持图片、文档、音频、视频等文件类型，最大50MB</small>
                    </div>

                    <div class="input-group">
                        <input type="text" id="messageContent" class="form-control" placeholder="输入消息内容..."
                            onkeypress="handleEnterKey(event)">
                        <button class="btn btn-success" onclick="sendMessage()">发送消息</button>
                        <button class="btn btn-primary" id="uploadFileBtn" onclick="uploadAndSendFile()"
                            style="display: none;">上传并发送
                        </button>
                    </div>

                    <div class="form-group" style="margin-top: 15px;">
                        <label>操作日志:</label>
                        <div class="log-area" id="logArea"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let websocket = null;
        let currentUserId = null;
        let currentRoomId = null;
        let isConnected = false;
        let sentMessages = new Map(); // 存储已发送的消息，用于去重和状态管理

        // 确保布局正确的函数
        function ensureLayoutCorrect() {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');
            const chatArea = document.querySelector('.chat-area');

            if (sidebar) {
                sidebar.style.display = 'block';
                sidebar.style.visibility = 'visible';
                sidebar.style.width = '300px';
                sidebar.style.flexShrink = '0';
                sidebar.style.minWidth = '300px';
                sidebar.style.maxWidth = '300px';
                sidebar.style.position = 'relative';
                sidebar.style.opacity = '1';
                sidebar.style.transform = 'none';
            }

            if (mainContent) {
                mainContent.style.display = 'flex';
                mainContent.style.height = '600px';
                mainContent.style.flexDirection = 'row';
                mainContent.style.width = '100%';
            }

            if (chatArea) {
                chatArea.style.flex = '1';
                chatArea.style.display = 'flex';
                chatArea.style.flexDirection = 'column';
                chatArea.style.minWidth = '0';
                chatArea.style.width = 'auto';
                chatArea.style.overflow = 'hidden';
            }

            // 强制重新计算布局
            if (mainContent) {
                mainContent.offsetHeight; // 触发重排
            }
        }

        // 定期检查布局的函数
        function startLayoutMonitor() {
            setInterval(() => {
                const sidebar = document.querySelector('.sidebar');
                if (sidebar && (sidebar.offsetWidth === 0 || sidebar.style.display === 'none')) {
                    console.warn('检测到侧边栏被隐藏，正在修复...');
                    ensureLayoutCorrect();
                }
            }, 1000);
        }

        // 页面加载完成后确保布局正确
        document.addEventListener('DOMContentLoaded', function () {
            ensureLayoutCorrect();
            startLayoutMonitor(); // 启动布局监控
        });

        // 窗口大小改变时确保布局正确
        window.addEventListener('resize', function () {
            ensureLayoutCorrect();
        });

        // 动态获取服务器地址
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;
        const WS_URL = `${protocol}//${host}:9090/ws`;
        const API_BASE_URL = `${window.location.protocol}//${host}:8083/api/chat`;

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logArea.textContent += logEntry;
            logArea.scrollTop = logArea.scrollHeight;
            console.log(logEntry);
        }

        // 连接WebSocket
        function connect() {
            const userId = document.getElementById('userId').value;
            const token = document.getElementById('authToken').value;

            if (!userId || !token) {
                log('请输入用户ID和认证Token', 'error');
                return;
            }

            try {
                // 如果已有连接，先关闭旧连接
                if (websocket) {
                    log('关闭现有WebSocket连接: ' + websocket.readyState);
                    websocket.close();
                }

                log('开始创建WebSocket连接: ' + WS_URL);
                websocket = new WebSocket(WS_URL);
                const connectionId = Date.now();

                log(`WebSocket对象已创建 (连接ID: ${connectionId}), 初始状态: ${websocket.readyState}`);

                websocket.onopen = function (event) {
                    log(`WebSocket连接已建立 (连接ID: ${connectionId}), 状态: ${websocket.readyState}`);
                    updateConnectionStatus(true);

                    // 发送认证消息
                    const authMessage = {
                        type: 'auth',
                        data: {
                            token: token
                        },
                        timestamp: Date.now()
                    };

                    websocket.send(JSON.stringify(authMessage));
                    log('已发送认证消息: ' + JSON.stringify(authMessage));
                };

                websocket.onmessage = function (event) {
                    try {
                        console.log('原始消息数据:', event.data);
                        const message = JSON.parse(event.data);
                        console.log('解析后的消息:', message);
                        handleWebSocketMessage(message);
                    } catch (e) {
                        console.error('JSON解析错误详情:', e);
                        log('解析消息失败: ' + event.data + ', 错误: ' + e.message, 'error');
                    }
                };

                websocket.onclose = function (event) {
                    log(`WebSocket连接已关闭: code=${event.code}, reason=${event.reason}`);
                    updateConnectionStatus(false);
                };

                websocket.onerror = function (error) {
                    log('WebSocket连接错误: ' + JSON.stringify(error), 'error');
                    updateConnectionStatus(false);
                };

            } catch (error) {
                log('创建WebSocket连接失败: ' + error, 'error');
            }
        }

        // 断开连接
        function disconnect() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            updateConnectionStatus(false);
            log('已断开WebSocket连接');
        }

        // 更新连接状态
        function updateConnectionStatus(connected) {
            isConnected = connected;
            const statusIndicator = document.getElementById('statusIndicator');
            const connectionStatus = document.getElementById('connectionStatus');

            if (connected) {
                statusIndicator.className = 'status-indicator status-online';
                connectionStatus.textContent = '已连接';
            } else {
                statusIndicator.className = 'status-indicator status-offline';
                connectionStatus.textContent = '未连接';
                document.getElementById('currentUserId').textContent = '-';
                document.getElementById('sessionId').textContent = '-';
                currentUserId = null;
            }
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            log('收到消息: ' + JSON.stringify(message));

            switch (message.type) {
                case 'auth_success':
                    handleAuthSuccess(message);
                    break;
                case 'auth_failure':
                    handleAuthFailure(message);
                    break;
                case 'private_message':
                    handlePrivateMessage(message);
                    break;
                case 'group_message':
                    handleGroupMessage(message);
                    break;
                case 'room_joined':
                    handleRoomJoined(message);
                    break;
                case 'room_left':
                    handleRoomLeft(message);
                    break;
                case 'user_joined':
                    handleUserJoined(message);
                    break;
                case 'user_left':
                    handleUserLeft(message);
                    break;
                case 'system_message':
                    handleSystemMessage(message);
                    break;
                case 'error':
                    handleError(message);
                    break;
                case 'heartbeat_response':
                    handleHeartbeatResponse(message);
                    break;
                case 'heartbeat_ack':
                    handleHeartbeatAck(message);
                    break;
                case 'join_room_success':
                    handleJoinRoomSuccess(message);
                    break;
                case 'leave_room_success':
                    handleLeaveRoomSuccess(message);
                    break;
                case 'message_ack':
                    handleMessageAck(message);
                    break;
                default:
                    log('未知消息类型: ' + message.type, 'warning');
            }
        }

        // 处理认证成功
        function handleAuthSuccess(message) {
            const data = message.data;
            currentUserId = data.userId;
            document.getElementById('currentUserId').textContent = data.userId;
            document.getElementById('sessionId').textContent = data.sessionId;

            // 更新页面标题显示当前用户
            document.title = `聊天测试 - 用户${data.userId} (${data.username})`;

            addMessage('system', `认证成功！欢迎 ${data.nickname || data.username} (ID: ${data.userId})`);
            log('用户认证成功: ' + data.username);
        }

        // 处理认证失败
        function handleAuthFailure(message) {
            const data = message.data;
            addMessage('error', `认证失败: ${data.message} (错误码: ${data.errorCode})`);
            log('认证失败: ' + data.message, 'error');
            disconnect();
        }

        // 处理私聊消息
        function handlePrivateMessage(message) {
            const data = message.data;
            const messageId = data.messageId;

            // 检查是否是自己发送的消息
            const isOwnMessage = data.senderId == currentUserId;

            // 构建引用消息数据（如果有）
            let referenceData = null;
            if (data.referenceMessageId) {
                referenceData = {
                    messageId: data.referenceMessageId,
                    senderId: data.referenceSenderId,
                    content: data.referenceContent || ''
                };
            }

            // 如果是自己的消息，检查是否已经显示过（避免重复）
            if (isOwnMessage && sentMessages.has(messageId)) {
                // 更新消息状态为已送达
                updateMessageStatus(messageId, 'delivered');
                log(`私聊消息已送达: messageId=${messageId}`);
                return;
            }

            // 显示消息
            if (isOwnMessage) {
                addMessage('sent', `[私聊给 ${data.receiverId}] ${data.content}`, data.sendTime, messageId, referenceData);
                // 标记为已送达
                updateMessageStatus(messageId, 'delivered');
            } else {
                addMessage('received', `[私聊] ${data.senderId}: ${data.content}`, data.sendTime, messageId, referenceData);
            }
        }

        // 处理群聊消息
        function handleGroupMessage(message) {
            const data = message.data;
            const messageId = data.messageId;

            // 检查是否是自己发送的消息
            const isOwnMessage = data.senderId == currentUserId;

            // 构建引用消息数据（如果有）
            let referenceData = null;
            if (data.referenceMessageId) {
                referenceData = {
                    messageId: data.referenceMessageId,
                    senderId: data.referenceSenderId,
                    content: data.referenceContent || ''
                };
            }

            // 如果是自己的消息，检查是否已经显示过（避免重复）
            if (isOwnMessage && sentMessages.has(messageId)) {
                // 更新消息状态为已送达
                updateMessageStatus(messageId, 'delivered');
                log(`群聊消息已送达: messageId=${messageId}`);
                return;
            }

            // 显示消息
            if (isOwnMessage) {
                addMessage('sent', `[群聊-${data.roomId}] ${data.content}`, data.sendTime, messageId, referenceData);
                // 标记为已送达
                updateMessageStatus(messageId, 'delivered');
            } else {
                addMessage('received', `[群聊-${data.roomId}] ${data.senderId}: ${data.content}`, data.sendTime, messageId, referenceData);
            }
        }

        // 处理房间加入成功
        function handleRoomJoined(message) {
            const data = message.data;
            currentRoomId = data.roomId;
            addMessage('system', `成功加入房间: ${data.roomId}`);
            log('成功加入房间: ' + data.roomId);
        }

        // 处理房间离开
        function handleRoomLeft(message) {
            const data = message.data;
            addMessage('system', `已离开房间: ${data.roomId}`);
            log('已离开房间: ' + data.roomId);
            if (currentRoomId === data.roomId) {
                currentRoomId = null;
            }
        }

        // 处理用户加入房间
        function handleUserJoined(message) {
            const data = message.data;
            addMessage('system', `用户 ${data.userId} 加入了房间 ${data.roomId}`);
        }

        // 处理用户离开房间
        function handleUserLeft(message) {
            const data = message.data;
            addMessage('system', `用户 ${data.userId} 离开了房间 ${data.roomId}`);
        }

        // 处理系统消息
        function handleSystemMessage(message) {
            const data = message.data;
            const content = data.content || data.message || '系统消息';
            const timestamp = data.timestamp || message.timestamp;

            // 系统消息通常包含更多信息
            let displayContent = content;

            // 如果有额外的系统消息类型信息
            if (data.systemType) {
                switch (data.systemType) {
                    case 'user_join':
                        displayContent = `👋 用户 ${data.userId || data.username} 加入了聊天`;
                        break;
                    case 'user_leave':
                        displayContent = `👋 用户 ${data.userId || data.username} 离开了聊天`;
                        break;
                    case 'room_created':
                        displayContent = `🏠 房间 "${data.roomName || data.roomId}" 已创建`;
                        break;
                    case 'room_deleted':
                        displayContent = `🗑️ 房间 "${data.roomName || data.roomId}" 已删除`;
                        break;
                    case 'maintenance':
                        displayContent = `🔧 系统维护通知: ${content}`;
                        break;
                    case 'announcement':
                        displayContent = `📢 系统公告: ${content}`;
                        break;
                    default:
                        displayContent = `ℹ️ ${content}`;
                }
            }

            addMessage('system', displayContent, timestamp);
            log(`收到系统消息: ${data.systemType || 'general'} - ${content}`);
        }

        // 处理错误消息
        function handleError(message) {
            const data = message.data;
            addMessage('error', `错误: ${data.message || data.error}`);
            log('收到错误消息: ' + (data.message || data.error), 'error');
        }

        // 处理心跳响应
        function handleHeartbeatResponse(message) {
            log('收到心跳响应');
        }

        // 处理心跳确认
        function handleHeartbeatAck(message) {
            const data = message.data;
            log(`收到心跳确认: status=${data.status}, timestamp=${data.timestamp}`);
        }

        // 处理加入房间成功
        function handleJoinRoomSuccess(message) {
            const data = message.data;
            const roomId = data.roomId;
            log(`成功加入房间: ${roomId}`);

            // 更新当前房间ID
            currentRoomId = roomId;

            // 更新界面显示（如果元素存在）
            const currentRoomElement = document.getElementById('currentRoomId');
            if (currentRoomElement) {
                currentRoomElement.textContent = roomId;
            }

            // 添加系统消息
            addMessage('system', `已成功加入房间: ${roomId}`);
        }

        // 处理离开房间成功
        function handleLeaveRoomSuccess(message) {
            const data = message.data;
            const roomId = data.roomId;
            log(`成功离开房间: ${roomId}`);

            // 如果离开的是当前房间，清空当前房间ID
            if (currentRoomId === roomId) {
                currentRoomId = null;
            }

            // 更新界面显示（如果元素存在）
            const currentRoomElement = document.getElementById('currentRoomId');
            if (currentRoomElement) {
                currentRoomElement.textContent = '-';
            }

            // 添加系统消息
            addMessage('system', `已成功离开房间: ${roomId}`);
        }

        // 处理消息确认
        function handleMessageAck(message) {
            const data = message.data;
            const messageId = data.messageId;
            const status = data.status;
            log(`消息发送确认: messageId=${messageId}, status=${status}`);

            // 更新消息状态
            if (status === 'sent') {
                updateMessageStatus(messageId, 'sent');
                // 更新本地记录的消息状态
                if (sentMessages.has(messageId)) {
                    const messageInfo = sentMessages.get(messageId);
                    messageInfo.status = 'sent';
                    sentMessages.set(messageId, messageInfo);
                }
                log(`消息 ${messageId} 已成功发送到服务器`);
            } else if (status === 'failed') {
                updateMessageStatus(messageId, 'failed');
                // 更新本地记录的消息状态
                if (sentMessages.has(messageId)) {
                    const messageInfo = sentMessages.get(messageId);
                    messageInfo.status = 'failed';
                    sentMessages.set(messageId, messageInfo);
                }
                log(`消息 ${messageId} 发送失败`, 'error');
            }
        }

        // 添加消息到界面
        function addMessage(type, content, timestamp, messageId, referenceData) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');

            let className = `message ${type}`;
            if (referenceData) {
                className = `message reply`;
            }
            messageDiv.className = className;

            // 如果有messageId，设置为元素的data属性，方便后续更新状态
            if (messageId) {
                messageDiv.setAttribute('data-message-id', messageId);
            }

            const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

            // 为发送的消息添加状态指示器
            let statusIndicator = '';
            if (type === 'sent' && messageId) {
                statusIndicator = '<span class="message-status" data-status="sending">发送中...</span>';
            }

            let messageHTML = '';

            // 添加引用消息预览
            if (referenceData) {
                messageHTML += `
                    <div class="reply-reference" onclick="highlightReferencedMessage('${referenceData.messageId}')">
                        <div class="sender">回复 ${referenceData.senderId}:</div>
                        <div class="content">${escapeHtml(referenceData.content)}</div>
                    </div>
                `;
            }

            messageHTML += `
                <div class="message-header">${time} - ${type.toUpperCase()} ${statusIndicator}</div>
                <div class="message-content">${escapeHtml(content)}</div>
            `;

            // 只为接收到的消息添加回复按钮，不为系统消息、错误消息和自己发送的消息添加
            if (type === 'received' && messageId) {
                messageHTML += `
                <div class="message-actions">
                    <button class="btn btn-primary" onclick="replyToMessage('${messageId}', '${currentUserId}', '${escapeHtml(content)}')">回复</button>
                </div>
            `;
            }

            messageDiv.innerHTML = messageHTML;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // HTML转义函数
        function escapeHtml(text) {
            if (!text) return '';
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 回复消息
        function replyToMessage(messageId, senderId, content) {
            document.getElementById('replyPreview').style.display = 'block';
            document.getElementById('replySender').textContent = senderId;
            document.getElementById('replyContent').textContent = content;
            document.getElementById('referenceMessageId').value = messageId;
            document.getElementById('referenceSenderId').value = senderId;

            document.getElementById('messageContent').focus();
            log(`设置回复消息引用: messageId=${messageId}, senderId=${senderId}`);
        }

        // 取消回复
        function cancelReply() {
            document.getElementById('replyPreview').style.display = 'none';
            document.getElementById('referenceMessageId').value = '';
            document.getElementById('referenceSenderId').value = '';
        }

        // 高亮引用的消息
        function highlightReferencedMessage(messageId) {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.style.backgroundColor = '#fff3cd';
                setTimeout(() => {
                    messageElement.style.backgroundColor = '';
                }, 2000);

                // 滚动到引用的消息
                messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        // 更新消息状态
        function updateMessageStatus(messageId, status) {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                const statusElement = messageElement.querySelector('.message-status');
                if (statusElement) {
                    statusElement.setAttribute('data-status', status);
                    switch (status) {
                        case 'sent':
                            statusElement.textContent = '已发送';
                            break;
                        case 'delivered':
                            statusElement.textContent = '已送达';
                            break;
                        case 'failed':
                            statusElement.textContent = '发送失败';
                            break;
                    }
                }
            }
        }

        // 发送消息
        function sendMessage() {
            if (!isConnected) {
                log('请先连接WebSocket', 'error');
                return;
            }

            const content = document.getElementById('messageContent').value.trim();
            const receiverId = document.getElementById('receiverId').value.trim();
            const messageType = document.getElementById('messageType').value;

            if (!content) {
                log('请输入消息内容', 'error');
                return;
            }

            // 生成消息ID
            const messageId = 'msg_' + Date.now() + '_' + currentUserId + '_' + Math.random().toString(36).substr(2, 9);

            const message = {
                type: 'chat',
                data: {
                    messageId: messageId,
                    messageType: messageType,
                    content: content,
                    sendTime: Date.now()
                },
                timestamp: Date.now()
            };

            // 检查是否有引用消息
            const referenceMessageId = document.getElementById('referenceMessageId').value;
            const referenceSenderId = document.getElementById('referenceSenderId').value;

            if (referenceMessageId && referenceSenderId) {
                message.data.referenceMessageId = referenceMessageId;
                message.data.referenceSenderId = parseInt(referenceSenderId);
                message.data.referenceContent = document.getElementById('replyContent').textContent;
            }

            // 设置接收者或房间
            if (receiverId) {
                message.data.receiverId = parseInt(receiverId);
            } else if (currentRoomId) {
                message.data.roomId = currentRoomId;
            } else {
                log('请先加入房间或指定接收者', 'error');
                return;
            }

            // 记录发送的消息
            sentMessages.set(messageId, {
                content: content,
                timestamp: Date.now(),
                status: 'sending'
            });

            // 构建引用数据用于显示
            let referenceData = null;
            if (referenceMessageId && referenceSenderId) {
                referenceData = {
                    messageId: referenceMessageId,
                    senderId: referenceSenderId,
                    content: document.getElementById('replyContent').textContent
                };
            }

            // 立即显示发送的消息
            if (receiverId) {
                addMessage('sent', `[私聊给 ${receiverId}] ${content}`, Date.now(), messageId, referenceData);
            } else {
                addMessage('sent', `[群聊-${currentRoomId}] ${content}`, Date.now(), messageId, referenceData);
            }

            // 发送消息
            websocket.send(JSON.stringify(message));

            // 清空输入框和回复引用
            document.getElementById('messageContent').value = '';
            cancelReply();

            // 确保侧边栏和布局始终正确
            ensureLayoutCorrect();

            // 延迟再次确保布局正确（防止异步操作影响布局）
            setTimeout(() => {
                ensureLayoutCorrect();
                log('布局已重置确保侧边栏可见');
            }, 100);

            log('已发送消息: ' + JSON.stringify(message));
        }

        // 处理回车键发送消息
        function handleEnterKey(event) {
            if (event.key === 'Enter') {
                sendMessage();
            }
        }

        // 发送心跳
        function sendHeartbeat() {
            if (!isConnected) {
                log('请先连接WebSocket', 'error');
                return;
            }

            const heartbeatMessage = {
                type: 'heartbeat',
                data: {},
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(heartbeatMessage));
            log('已发送心跳消息');
        }

        // 加入房间
        function joinRoom() {
            if (!isConnected) {
                log('请先连接WebSocket', 'error');
                return;
            }

            const roomId = document.getElementById('roomId').value.trim();
            if (!roomId) {
                log('请输入房间ID', 'error');
                return;
            }

            const message = {
                type: 'join_room',
                data: { roomId: roomId },
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(message));
            log('已发送加入房间请求: ' + roomId);
        }

        // 离开房间
        function leaveRoom() {
            if (!isConnected) {
                log('请先连接WebSocket', 'error');
                return;
            }

            if (!currentRoomId) {
                log('当前未在任何房间中', 'error');
                return;
            }

            const message = {
                type: 'leave_room',
                data: { roomId: currentRoomId },
                timestamp: Date.now()
            };

            websocket.send(JSON.stringify(message));
            log('已发送离开房间请求: ' + currentRoomId);
        }

        // 选择房间
        function selectRoom(roomId) {
            document.getElementById('roomId').value = roomId;

            // 更新房间列表的选中状态
            const roomItems = document.querySelectorAll('.room-item');
            roomItems.forEach(item => item.classList.remove('active'));

            const selectedItem = Array.from(roomItems).find(item =>
                item.textContent.includes(roomId)
            );
            if (selectedItem) {
                selectedItem.classList.add('active');
            }

            log('已选择房间: ' + roomId);
        }

        // 加载公开房间
        function loadPublicRooms() {
            // 这里可以通过API获取房间列表，暂时使用静态数据
            const roomList = document.getElementById('roomList');
            roomList.innerHTML = `
                <div class="room-item" onclick="selectRoom('public_general')">public_general - 大厅</div>
                <div class="room-item" onclick="selectRoom('public_tech')">public_tech - 技术交流</div>
                <div class="room-item" onclick="selectRoom('public_random')">public_random - 随便聊聊</div>
                <div class="room-item" onclick="selectRoom('room_001')">room_001 - 测试房间</div>
                <div class="room-item" onclick="selectRoom('room_002')">room_002 - 开发讨论</div>
            `;
            log('已加载公开房间列表');
        }



        // 文件上传相关功能
        document.getElementById('messageType').addEventListener('change', function () {
            const messageType = this.value;
            const fileUploadSection = document.getElementById('fileUploadSection');
            const uploadFileBtn = document.getElementById('uploadFileBtn');

            if (messageType === 'file' || messageType === 'image') {
                fileUploadSection.style.display = 'block';
                uploadFileBtn.style.display = 'inline-block';
            } else {
                fileUploadSection.style.display = 'none';
                uploadFileBtn.style.display = 'none';
            }
        });

        // API调用辅助函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                const data = await response.json();
                log(`API调用: ${url} - 状态: ${response.status}`);
                return data;
            } catch (error) {
                log(`API调用失败: ${url} - 错误: ${error.message}`, 'error');
                throw error;
            }
        }

        // 获取在线用户
        async function getOnlineUsers() {
            try {
                const data = await apiCall(`${API_BASE_URL}/online-users`);
                if (data.success) {
                    addMessage('system', `在线用户: ${JSON.stringify(data.data)}`);
                    log('获取在线用户成功: ' + JSON.stringify(data.data));
                } else {
                    log('获取在线用户失败: ' + data.message, 'error');
                }
            } catch (error) {
                log('获取在线用户异常: ' + error.message, 'error');
            }
        }

        // 获取用户状态
        async function getUserStatus() {
            const userId = document.getElementById('userId').value;
            if (!userId) {
                log('请输入用户ID', 'error');
                return;
            }

            try {
                const data = await apiCall(`${API_BASE_URL}/users/${userId}/status`);
                if (data.success) {
                    addMessage('system', `用户状态: ${JSON.stringify(data.data, null, 2)}`);
                    log('获取用户状态成功: ' + JSON.stringify(data.data));
                } else {
                    log('获取用户状态失败: ' + data.message, 'error');
                }
            } catch (error) {
                log('获取用户状态异常: ' + error.message, 'error');
            }
        }

        // 创建测试房间
        async function createTestRoom() {
            const userId = document.getElementById('userId').value;
            if (!userId) {
                log('请输入用户ID', 'error');
                return;
            }

            const roomData = {
                roomId: 'test_room_' + Date.now(),
                roomName: '测试房间_' + new Date().toLocaleTimeString(),
                description: '通过测试页面创建的房间',
                roomType: 'public',
                creatorId: parseInt(userId),
                maxUsers: 50
            };

            try {
                const data = await apiCall(`${API_BASE_URL}/rooms`, {
                    method: 'POST',
                    body: JSON.stringify(roomData)
                });

                if (data.success) {
                    addMessage('system', `房间创建成功: ${data.data.roomId}`);
                    log('房间创建成功: ' + JSON.stringify(data.data));

                    // 添加到房间列表
                    const roomList = document.getElementById('roomList');
                    const roomItem = document.createElement('div');
                    roomItem.className = 'room-item';
                    roomItem.onclick = () => selectRoom(data.data.roomId);
                    roomItem.textContent = `${data.data.roomId} - ${data.data.roomName}`;
                    roomList.appendChild(roomItem);
                } else {
                    log('房间创建失败: ' + data.message, 'error');
                }
            } catch (error) {
                log('房间创建异常: ' + error.message, 'error');
            }
        }

        // 加载公开房间
        async function loadPublicRooms() {
            try {
                const data = await apiCall(`${API_BASE_URL}/rooms/public?page=1&size=10`);
                if (data.success && data.data) {
                    const roomList = document.getElementById('roomList');
                    roomList.innerHTML = ''; // 清空现有列表

                    data.data.forEach(room => {
                        const roomItem = document.createElement('div');
                        roomItem.className = 'room-item';
                        roomItem.onclick = () => selectRoom(room.roomId);
                        roomItem.textContent = `${room.roomId} - ${room.roomName}`;
                        roomList.appendChild(roomItem);
                    });

                    log('公开房间加载成功: ' + data.data.length + ' 个房间');
                } else {
                    log('加载公开房间失败: ' + (data.message || '无数据'), 'error');
                }
            } catch (error) {
                log('加载公开房间异常: ' + error.message, 'error');
            }
        }


        // 监听消息类型变化，显示/隐藏文件上传区域
        document.getElementById('messageType').addEventListener('change', function () {
            const messageType = this.value;
            const fileUploadSection = document.getElementById('fileUploadSection');
            const uploadFileBtn = document.getElementById('uploadFileBtn');
            const messageContent = document.getElementById('messageContent');

            if (messageType === 'file' || messageType === 'image') {
                fileUploadSection.style.display = 'block';
                uploadFileBtn.style.display = 'inline-block';
                messageContent.placeholder = '可选：输入文件描述...';
            } else {
                fileUploadSection.style.display = 'none';
                uploadFileBtn.style.display = 'none';
                messageContent.placeholder = '输入消息内容...';
            }
        });

        // 上传文件并发送消息
        async function uploadAndSendFile() {
            if (!isConnected) {
                log('请先连接WebSocket', 'error');
                return;
            }

            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                log('请选择要上传的文件', 'error');
                return;
            }

            // 检查文件大小（50MB限制）
            const maxSize = 50 * 1024 * 1024; // 50MB
            if (file.size > maxSize) {
                log('文件大小超过50MB限制', 'error');
                return;
            }

            const messageType = document.getElementById('messageType').value;
            const description = document.getElementById('messageContent').value.trim();
            const receiverId = document.getElementById('receiverId').value;

            try {
                log('开始上传文件: ' + file.name);

                // 创建FormData对象
                const formData = new FormData();
                formData.append('file', file);

                // 添加用户ID参数
                const userId = document.getElementById('userId').value;
                if (userId) {
                    formData.append('userId', userId);
                }

                // 根据消息类型选择上传接口
                let uploadUrl;
                if (messageType === 'image') {
                    uploadUrl = `${window.location.protocol}//${window.location.hostname}:8083/api/chat/files/upload/avatar`;
                } else {
                    uploadUrl = `${window.location.protocol}//${window.location.hostname}:8083/api/chat/files/upload/chat`;
                    if (currentRoomId) {
                        formData.append('roomId', currentRoomId);
                    }
                }

                // 上传文件
                const response = await fetch(uploadUrl, {
                    method: 'POST',
                    body: formData
                });

                const uploadResult = await response.json();

                if (uploadResult.success && uploadResult.data) {
                    const fileInfo = uploadResult.data;
                    log('文件上传成功: ' + fileInfo.fileId);

                    // 构建文件信息对象
                    const fileData = {
                        fileId: fileInfo.fileId,
                        fileName: fileInfo.fileName,
                        fileSize: fileInfo.fileSize,
                        fileType: fileInfo.fileType,
                        downloadUrl: fileInfo.downloadUrl,
                        thumbnailUrl: fileInfo.thumbnailUrl
                    };

                    // 构建文件消息
                    const fileMessage = {
                        type: 'chat',
                        data: {
                            messageType: messageType,
                            content: description || file.name,
                            extraData: JSON.stringify(fileData),
                            sendTime: Date.now()
                        },
                        timestamp: Date.now()
                    };

                    // 设置接收者
                    if (receiverId) {
                        fileMessage.data.receiverId = parseInt(receiverId);
                    } else if (currentRoomId) {
                        fileMessage.data.roomId = currentRoomId;
                    } else {
                        log('请先加入房间或指定接收者', 'error');
                        return;
                    }

                    // 发送文件消息
                    websocket.send(JSON.stringify(fileMessage));

                    // 显示发送的文件消息
                    const displayContent = createFileMessageContent(fileInfo, description);
                    if (receiverId) {
                        addMessage('sent', `[私聊给 ${receiverId}] ${displayContent}`);
                    } else {
                        addMessage('sent', `[群聊-${currentRoomId}] ${displayContent}`);
                    }

                    // 清空输入
                    document.getElementById('messageContent').value = '';
                    fileInput.value = '';

                    log('文件消息发送成功');
                } else {
                    log('文件上传失败: ' + (uploadResult.message || '未知错误'), 'error');
                }
            } catch (error) {
                log('文件上传异常: ' + error.message, 'error');
            }
        }

        // 创建文件消息内容显示
        function createFileMessageContent(fileInfo, description) {
            const fileSize = formatFileSize(fileInfo.fileSize);
            const fileType = fileInfo.fileType || 'unknown';

            let content = `📎 ${fileInfo.fileName} (${fileSize})`;

            if (description) {
                content += `\n💬 ${description}`;
            }

            // 如果是图片，添加缩略图链接
            if (fileType.startsWith('image/') && fileInfo.thumbnailUrl) {
                content += `\n🖼️ <a href="${fileInfo.downloadUrl}" target="_blank">查看图片</a>`;
            } else {
                content += `\n📥 <a href="${fileInfo.downloadUrl}" target="_blank">下载文件</a>`;
            }

            return content;
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            // 检查输入是否有效
            if (bytes === null || bytes === undefined || isNaN(bytes)) {
                return '未知大小';
            }

            if (bytes === 0) return '0 Bytes';

            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));

            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 处理文件消息显示
        function handleFileMessage(message) {
            const data = message.data;
            const fileInfo = {
                fileId: data.fileId,
                fileName: data.fileName,
                fileSize: data.fileSize,
                fileType: data.fileType,
                downloadUrl: data.downloadUrl,
                thumbnailUrl: data.thumbnailUrl
            };

            const displayContent = createFileMessageContent(fileInfo, data.content);

            if (data.roomId) {
                addMessage('received', `[群聊-${data.roomId}] ${data.senderId}: ${displayContent}`, data.sendTime);
            } else {
                addMessage('received', `[私聊] ${data.senderId}: ${displayContent}`, data.sendTime);
            }
        }

        // 更新消息处理函数，添加文件消息处理
        const originalHandleWebSocketMessage = handleWebSocketMessage;
        handleWebSocketMessage = function (message) {
            // 处理文件消息
            if (message.type === 'private_message' || message.type === 'group_message') {
                const data = message.data;
                if (data.messageType === 'file' || data.messageType === 'image') {
                    handleFileMessage(message);
                    return;
                }
            }

            // 调用原始处理函数
            originalHandleWebSocketMessage(message);
        };
    </script>
</body>

</html>