package com.xiang.chat.feign;

import com.xiang.chat.code.R;
import com.xiang.chat.dto.RegisterRequest;
import com.xiang.chat.dto.RegisterResponse;
import com.xiang.chat.dto.UserInfo;
import com.xiang.chat.dto.ValidationResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;


@FeignClient(name = "auth-service", 
             fallback = AuthFeignService.AuthFeignServiceFallback.class,
             configuration = com.xiang.chat.config.AuthFeignConfig.class)
public interface AuthFeignService {

    /**
     * 验证JWT token
     */
    @PostMapping("/api/jwt/validate")
    R<ValidationResult> validateToken(@RequestParam("token") String token);

    /**
     * 获取用户信息
     */
    @GetMapping("/api/jwt/userinfo")
    R<UserInfo> getUserInfo();

    /**
     * 用户注册
     */
    @PostMapping("/api/register")
    R<RegisterResponse> register(@Valid @RequestBody RegisterRequest registerRequest);

    /**
     * 检查用户名是否可用
     */
    @GetMapping("/api/register/check-username")
    R<Boolean> checkUsername(@RequestParam("username") String username);

    /**
     * 检查邮箱是否可用
     */
    @GetMapping("/api/register/check-email")
    R<Boolean> checkEmail(@RequestParam("email") String email);

    /**
     * 根据用户ID获取用户信息
     */
    @GetMapping("/api/users/{userId}")
    R<UserInfo> getUserInfoById(@PathVariable("userId") Long userId);

    /**
     * 批量获取用户信息
     */
    @PostMapping("/api/users/batch")
    R<java.util.List<UserInfo>> getUserInfoBatch(@RequestBody java.util.Map<String, Object> request);

    /**
     * 根据用户名获取用户信息
     */
    @GetMapping("/api/users/username/{username}")
    R<UserInfo> getUserInfoByUsername(@PathVariable("username") String username);



    /**
     * Fallback实现
     */
    @Component
    class AuthFeignServiceFallback implements AuthFeignService {

        @Override
        public R<ValidationResult> validateToken(String token) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<UserInfo> getUserInfo() {
            return R.error("认证服务不可用");
        }

        @Override
        public R<RegisterResponse> register(RegisterRequest registerRequest) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<Boolean> checkUsername(String username) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<Boolean> checkEmail(String email) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<UserInfo> getUserInfoById(Long userId) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<java.util.List<UserInfo>> getUserInfoBatch(java.util.Map<String, Object> request) {
            return R.error("认证服务不可用，请稍后重试");
        }

        @Override
        public R<UserInfo> getUserInfoByUsername(String username) {
            return R.error("认证服务不可用，请稍后重试");
        }


    }
}
