<!DOCTYPE html>
<html lang="zh-CN" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            font-size: 24px;
        }

        .user-info {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .user-name {
            font-weight: 500;
        }

        .logout-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            cursor: pointer;
            transition: background 0.3s;
        }

        .logout-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .test-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 15px;
            cursor: pointer;
            transition: background 0.3s;
            font-size: 12px;
            margin-left: 10px;
        }

        .test-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .chat-container {
            flex: 1;
            display: flex;
            max-height: calc(100vh - 70px);
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }

        .sidebar-header h3 {
            color: #333;
            margin-bottom: 10px;
        }

        .online-users {
            flex: 1;
            overflow-y: auto;
        }

        .user-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f0f0f0;
            cursor: pointer;
            transition: background 0.2s;
            position: relative;
        }

        .user-item:hover {
            background: #f5f5f5;
        }

        .user-item.active {
            background: #e3f2fd;
            border-left: 3px solid #2196f3;
        }

        .user-item.self {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .user-item.self::after {
            content: "(我)";
            position: absolute;
            right: 20px;
            font-size: 12px;
            color: #999;
        }

        .chat-placeholder {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 16px;
        }

        .main-chat {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: white;
        }

        .chat-header {
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
            background: #fafafa;
        }

        .chat-messages {
            flex: 1;
            padding: 20px;
            overflow-y: auto;
            background: #f9f9f9;
        }

        .message {
            margin-bottom: 15px;
            display: flex;
            align-items: flex-start;
            gap: 10px;
        }

        .message.own {
            flex-direction: row-reverse;
        }

        .message-content {
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            background: white;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        }

        .message.own .message-content {
            background: #667eea;
            color: white;
        }

        .message-info {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .message.own .message-info {
            color: rgba(255, 255, 255, 0.8);
        }

        .chat-input {
            padding: 20px;
            border-top: 1px solid #e0e0e0;
            background: white;
        }

        .input-container {
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 25px;
            font-size: 14px;
            outline: none;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 500;
            transition: transform 0.2s;
        }

        .send-btn:hover {
            transform: translateY(-1px);
        }

        .status {
            padding: 10px 20px;
            background: #e8f5e8;
            color: #2e7d32;
            text-align: center;
            font-size: 14px;
        }

        .status.error {
            background: #ffebee;
            color: #c62828;
        }

        /* 消息类型样式 */
        .message.sent {
            display: flex;
            justify-content: flex-end;
        }

        .message.sent .message-content {
            background: #667eea;
            color: white;
        }

        .message.received {
            display: flex;
            justify-content: flex-start;
        }

        .message.received .message-content {
            background: white;
            color: #333;
        }

        .message.sent .message-info {
            color: rgba(255, 255, 255, 0.8);
        }

        /* 引用回复相关样式 */
        .reply-preview {
            display: none;
            background: #e9ecef;
            border-left: 3px solid #6f42c1;
            padding: 10px;
            margin-bottom: 10px;
            border-radius: 5px;
            position: relative;
        }

        .reply-preview .close-btn {
            position: absolute;
            top: 5px;
            right: 10px;
            cursor: pointer;
            font-size: 18px;
            color: #6c757d;
        }

        .reply-preview .sender {
            font-weight: bold;
            color: #6f42c1;
        }

        .reply-preview .content {
            margin-top: 5px;
            color: #6c757d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message.reply {
            border-left: 3px solid #6f42c1;
            background: #f8f9ff;
        }

        .reply-reference {
            background: #e9ecef;
            border-left: 3px solid #6f42c1;
            padding: 8px;
            margin-bottom: 8px;
            border-radius: 3px;
            cursor: pointer;
        }

        .reply-reference:hover {
            background: #dee2e6;
        }

        .reply-reference .sender {
            font-size: 12px;
            font-weight: bold;
            color: #6f42c1;
        }

        .reply-reference .content {
            font-size: 12px;
            color: #6c757d;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .message-actions {
            margin-top: 5px;
            text-align: right;
        }

        .reply-btn {
            background: #6f42c1;
            color: white;
            border: none;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            cursor: pointer;
        }

        .reply-btn:hover {
            background: #5a2d91;
        }
    </style>
</head>

<body>
    <div class="header">
        <h1>💬 聊天系统</h1>
        <div class="user-info">
            <span class="user-name">欢迎，<span th:text="${username ?: 'Guest'}">用户</span></span>
            <span th:if="${tokenType}" class="token-info" style="font-size: 12px; opacity: 0.8;">
                (OAuth2 认证)
            </span>
            <button type="button" class="test-btn" onclick="testBatchUserInfo()">测试JWT传递</button>
            <form th:action="@{/logout}" method="post" style="display: inline;">
                <button type="submit" class="logout-btn">退出登录</button>
            </form>
        </div>
    </div>

    <div class="chat-container">
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>在线用户</h3>
                <div id="onlineCount">0 人在线</div>
                <div style="font-size: 12px; color: #666; margin-top: 5px;">点击用户开始私聊</div>
            </div>
            <div class="online-users" id="onlineUsers">
                <!-- 在线用户列表 -->
            </div>
        </div>

        <div class="main-chat">
            <div class="chat-header">
                <h3 id="chatTitle">选择用户开始私聊</h3>
                <div id="chatSubtitle" style="font-size: 14px; color: #666; margin-top: 5px;"></div>
            </div>

            <div id="status" class="status" style="display: none;"></div>

            <div class="chat-messages" id="messages">
                <div class="chat-placeholder" id="chatPlaceholder">
                    <div>
                        <div style="font-size: 48px; margin-bottom: 20px;">💬</div>
                        <div>选择一个用户开始私聊</div>
                    </div>
                </div>
            </div>

            <div class="chat-input" id="chatInput" style="display: none;">
                <!-- 回复预览区域 -->
                <div class="reply-preview" id="replyPreview">
                    <span class="close-btn" onclick="cancelReply()">×</span>
                    <div>回复 <span class="sender" id="replySender"></span>:</div>
                    <div class="content" id="replyContent"></div>
                    <input type="hidden" id="referenceMessageId">
                    <input type="hidden" id="referenceSenderId">
                </div>

                <div class="input-container">
                    <input type="text" id="messageInput" class="message-input" placeholder="输入私聊消息..." maxlength="500">
                    <button id="sendBtn" class="send-btn">发送</button>
                </div>
            </div>
        </div>
        <div class="form-hidden" style="display: none;">
            <input type="hidden" name="username" th:value="${username}">
            <input type="hidden" name="accessToken" th:value="${accessToken}">
            <input type="hidden" name="tokenType" th:value="${tokenType}">
        </div>
    </div>

    <script>
        var formDom = document.querySelector('.form-hidden');
        const username = formDom.querySelector('input[name="username"]').value;
        const accessToken = formDom.querySelector('input[name="accessToken"]').value;
        const tokenType = formDom.querySelector('input[name="tokenType"]').value;
        let websocket = null;
        let isConnected = false;
        let currentChatUser = null; // 当前私聊的用户
        let chatHistory = new Map(); // 存储每个用户的聊天历史

        // 调试信息
        console.log('页面变量初始化:', {
            username: username,
            tokenType: tokenType,
            accessTokenLength: accessToken ? accessToken.length : 0,
            accessTokenPreview: accessToken ? accessToken.substring(0, 20) + '...' : 'empty'
        });

        // 初始化WebSocket连接
        function initWebSocket() {
            try {
                websocket = new WebSocket('ws://localhost:9090/ws');

                websocket.onopen = function () {
                    console.log('WebSocket连接已建立');
                    isConnected = true;
                    showStatus('已连接到服务器', false);

                    // 使用JWT token进行认证
                    const authMessage = {
                        type: 'auth',
                        data: {
                            token: accessToken,
                            tokenType: tokenType,
                            username: username
                        },
                        timestamp: Date.now()
                    };

                    console.log('发送JWT认证消息:', {
                        type: 'auth',
                        username: username,
                        tokenType: tokenType,
                        tokenLength: accessToken ? accessToken.length : 0
                    });

                    websocket.send(JSON.stringify(authMessage));
                };

                websocket.onmessage = function (event) {
                    const message = JSON.parse(event.data);
                    handleMessage(message);
                };

                websocket.onclose = function () {
                    console.log('WebSocket连接已关闭');
                    isConnected = false;
                    showStatus('连接已断开，正在重连...', true);

                    // 3秒后重连
                    setTimeout(initWebSocket, 3000);
                };

                websocket.onerror = function (error) {
                    console.error('WebSocket错误:', error);
                    showStatus('连接错误', true);
                };
            } catch (error) {
                console.error('WebSocket初始化失败:', error);
                showStatus('连接失败', true);
            }
        }

        // 处理接收到的消息
        function handleMessage(message) {
            console.log('收到消息:', message);

            switch (message.type) {
                case 'auth_success':
                    console.log('认证成功');
                    // 保存当前用户ID
                    if (message.data && message.data.userId) {
                        currentUserId = message.data.userId;
                        console.log('当前用户ID:', currentUserId);
                    }
                    showStatus('认证成功', false);
                    setTimeout(() => hideStatus(), 2000);
                    // 认证成功后，请求当前在线用户列表
                    requestOnlineUsers();
                    break;
                case 'auth_failure':
                    console.log('认证失败:', message.data.message);
                    showStatus('认证失败: ' + message.data.message, true);
                    break;
                case 'private_message':
                    handlePrivateMessage(message.data);
                    break;
                case 'user_list':
                case 'online_users':
                    // 处理用户列表更新（支持两种消息类型）
                    updateUserList(message.data.users);
                    break;
                case 'system_message':
                    displaySystemMessage(message.data.message);
                    break;
                default:
                    console.log('未知消息类型:', message.type);
            }
        }

        // 发送私聊消息
        function sendMessage() {
            const input = document.getElementById('messageInput');
            const messageText = input.value.trim();

            if (!messageText || !isConnected || !currentChatUser) {
                return;
            }

            // 生成消息ID
            const messageId = 'msg_' + Date.now() + '_' + (currentUserId || 'unknown') + '_' + Math.random().toString(36).substr(2, 9);

            const message = {
                type: 'chat',
                data: {
                    messageId: messageId,
                    content: messageText,
                    receiverId: currentChatUser.id,
                    messageType: 'text',
                    sendTime: Date.now()
                },
                timestamp: Date.now()
            };

            // 检查是否有引用消息
            const referenceMessageId = document.getElementById('referenceMessageId').value;
            const referenceSenderId = document.getElementById('referenceSenderId').value;

            if (referenceMessageId && referenceSenderId) {
                message.data.referenceMessageId = referenceMessageId;
                message.data.referenceSenderId = parseInt(referenceSenderId);
                message.data.referenceContent = document.getElementById('replyContent').textContent;
            }

            // 构建引用数据用于显示
            let referenceData = null;
            if (referenceMessageId && referenceSenderId) {
                referenceData = {
                    messageId: referenceMessageId,
                    senderId: referenceSenderId,
                    content: document.getElementById('replyContent').textContent
                };
            }

            // 记录发送的消息
            sentMessages.set(messageId, {
                content: messageText,
                timestamp: Date.now(),
                status: 'sending'
            });

            // 立即显示发送的消息
            addMessage('sent', messageText, Date.now(), messageId, null, referenceData, null);

            // 发送消息
            websocket.send(JSON.stringify(message));

            console.log('消息已发送:', message);
            input.value = '';

            // 清空回复引用
            cancelReply();
        }

        // 添加消息到界面 - 支持引用回复
        function addMessage(type, content, timestamp, messageId, senderName, referenceData, senderId) {
            console.log('addMessage被调用:', { type, content, timestamp, messageId, senderName, referenceData, senderId });

            const messagesDiv = document.getElementById('messages');
            if (!messagesDiv) {
                console.error('找不到messages元素');
                return;
            }

            const messageDiv = document.createElement('div');
            let className = `message ${type}`;
            if (referenceData) {
                className = `message reply`;
            }
            messageDiv.className = className;

            // 如果有messageId，设置为元素的data属性
            if (messageId) {
                messageDiv.setAttribute('data-message-id', messageId);
            }

            const time = timestamp ? new Date(timestamp).toLocaleTimeString() : new Date().toLocaleTimeString();

            // 根据消息类型显示不同的发送者信息
            let senderDisplay = '';
            if (type === 'sent') {
                senderDisplay = '我';
            } else if (type === 'received') {
                senderDisplay = senderName || '未知用户';
            }

            let messageHTML = '';

            // 添加引用消息预览
            if (referenceData) {
                console.log('添加引用消息预览:', referenceData);
                messageHTML += `
                    <div class="reply-reference" onclick="highlightReferencedMessage('${referenceData.messageId}')">
                        <div class="sender">回复 ${referenceData.senderId}:</div>
                        <div class="content">${escapeHtml(referenceData.content)}</div>
                    </div>
                `;
            } else {
                console.log('没有引用数据，不添加引用预览');
            }

            messageHTML += `
                <div class="message-content">
                    <div class="message-info">${senderDisplay} • ${time}</div>
                    <div>${escapeHtml(content)}</div>
                </div>
            `;

            // 只为接收到的消息添加回复按钮
            if (type === 'received' && messageId && senderId) {
                messageHTML += `
                    <div class="message-actions">
                        <button class="reply-btn" onclick="replyToMessage('${messageId}', '${senderId}', '${escapeHtml(content)}')">回复</button>
                    </div>
                `;
            }

            messageDiv.innerHTML = messageHTML;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;

            console.log('消息已添加到DOM，当前消息数量:', messagesDiv.children.length);
        }

        // 显示消息 - 保留原有接口兼容性
        function displayMessage(data) {
            console.log('displayMessage被调用，数据:', data);

            const type = data.isOwn ? 'sent' : 'received';
            const senderName = data.isOwn ? null : data.sender;

            addMessage(type, data.content, data.timestamp, null, senderName, null, null);
        }

        // 回复消息
        function replyToMessage(messageId, senderId, content) {
            document.getElementById('replyPreview').style.display = 'block';
            document.getElementById('replySender').textContent = senderId;
            document.getElementById('replyContent').textContent = content;
            document.getElementById('referenceMessageId').value = messageId;
            document.getElementById('referenceSenderId').value = senderId;

            document.getElementById('messageInput').focus();
            console.log(`设置回复消息引用: messageId=${messageId}, senderId=${senderId}`);
        }

        // 取消回复
        function cancelReply() {
            document.getElementById('replyPreview').style.display = 'none';
            document.getElementById('referenceMessageId').value = '';
            document.getElementById('referenceSenderId').value = '';
        }

        // 高亮引用的消息
        function highlightReferencedMessage(messageId) {
            const messageElement = document.querySelector(`[data-message-id="${messageId}"]`);
            if (messageElement) {
                messageElement.style.backgroundColor = '#fff3cd';
                setTimeout(() => {
                    messageElement.style.backgroundColor = '';
                }, 2000);

                // 滚动到引用的消息
                messageElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        // 处理私聊消息 - 参考index_test.html的实现
        function handlePrivateMessage(data) {
            console.log('收到私聊消息数据:', data);

            const messageId = data.messageId;
            const senderId = data.senderId;
            const receiverId = data.receiverId;
            const content = data.content;
            const timestamp = data.sendTime || Date.now();

            // 检查是否是自己发送的消息
            const isOwnMessage = senderId == getCurrentUserId();

            console.log('消息详情:', {
                messageId,
                senderId,
                receiverId,
                content,
                timestamp,
                isOwnMessage,
                currentUserId: getCurrentUserId()
            });

            // 从在线用户列表中查找发送者信息
            let senderName = senderId.toString();
            const userListDiv = document.getElementById('onlineUsers');
            if (userListDiv.userData) {
                const senderUser = userListDiv.userData.find(user =>
                    user.id == senderId
                );
                if (senderUser) {
                    senderName = senderUser.nickname || senderUser.username || senderId.toString();
                }
            }

            // 构建引用消息数据（如果有）
            let referenceData = null;
            if (data.referenceMessageId) {
                referenceData = {
                    messageId: data.referenceMessageId,
                    senderId: data.referenceSenderId,
                    content: data.referenceContent || ''
                };
                console.log('构建引用数据:', referenceData);
            } else {
                console.log('没有引用数据');
            }

            // 显示消息
            if (isOwnMessage) {
                // 如果是自己的消息，检查是否已经显示过（避免重复）
                if (sentMessages.has(messageId)) {
                    console.log(`私聊消息已送达: messageId=${messageId}`);
                    return;
                }
                // 自己发送的消息
                addMessage('sent', content, timestamp, messageId, null, referenceData, null);
            } else {
                // 别人发送的消息，传递发送者名称和ID
                addMessage('received', content, timestamp, messageId, senderName, referenceData, senderId);
            }

            // 添加到聊天历史
            const messageData = {
                sender: senderName,
                content: content,
                timestamp: timestamp,
                isOwn: isOwnMessage
            };

            // 根据是否是自己的消息来决定使用哪个用户ID作为key
            const chatUserId = isOwnMessage ? receiverId : senderId;
            addToChatHistory(String(chatUserId), messageData);
        }

        // 全局变量存储当前用户ID
        let currentUserId = null;

        // 跟踪已发送的消息，避免重复显示
        const sentMessages = new Map();

        // 获取当前用户ID的辅助函数
        function getCurrentUserId() {
            if (currentUserId) {
                return currentUserId.toString();
            }

            // 从在线用户列表中查找当前用户的ID
            const userListDiv = document.getElementById('onlineUsers');
            if (userListDiv.userData) {
                const currentUser = userListDiv.userData.find(user =>
                    user.username === username || user.nickname === username
                );
                if (currentUser) {
                    currentUserId = currentUser.id;
                    return currentUser.id.toString();
                }
            }
            return null;
        }

        // 显示系统消息
        function displaySystemMessage(message) {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = 'message system';
            messageDiv.innerHTML = `
                <div class="message-content" style="background: #f0f0f0; color: #666; text-align: center;">
                    ${escapeHtml(message)}
                </div>
            `;
            messagesDiv.appendChild(messageDiv);
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
        }

        // 更新用户列表
        function updateUserList(users) {
            const userListDiv = document.getElementById('onlineUsers');
            const countDiv = document.getElementById('onlineCount');

            if (!users || !Array.isArray(users)) {
                console.warn('用户列表数据无效:', users);
                countDiv.textContent = '0 人在线';
                userListDiv.innerHTML = '<div class="user-item">暂无在线用户</div>';
                return;
            }

            countDiv.textContent = users.length + ' 人在线';

            // 保存用户数据供其他函数使用
            userListDiv.userData = users;

            userListDiv.innerHTML = '';
            users.forEach(user => {
                const userDiv = document.createElement('div');

                // 处理用户信息显示
                let displayName = '';
                let userObj = null;

                if (typeof user === 'object' && user !== null) {
                    userObj = user;
                    displayName = user.nickname || user.username || '未知用户';
                    userDiv.title = `用户名: ${user.username || '未知'}\n昵称: ${user.nickname || '未设置'}\nID: ${user.id || '未知'}`;
                } else if (typeof user === 'string' || typeof user === 'number') {
                    userObj = { id: user, username: user.toString(), nickname: user.toString() };
                    displayName = user.toString();
                } else {
                    displayName = '未知用户';
                }

                // 检查是否是当前用户
                const isCurrentUser = userObj && (userObj.username === username || userObj.nickname === username);

                if (isCurrentUser) {
                    userDiv.className = 'user-item self';
                } else {
                    userDiv.className = 'user-item';
                    // 添加点击事件开始私聊
                    userDiv.addEventListener('click', () => startPrivateChat(userObj));
                }

                // 检查是否是当前聊天用户
                if (currentChatUser && userObj && userObj.id === currentChatUser.id) {
                    userDiv.classList.add('active');
                }

                userDiv.textContent = displayName;
                userListDiv.appendChild(userDiv);
            });

            console.log('用户列表已更新:', users.length, '个用户');
        }

        // 开始私聊
        function startPrivateChat(user) {
            if (!user || !user.id) {
                console.warn('无效的用户信息:', user);
                return;
            }

            currentChatUser = user;

            // 更新聊天标题
            const chatTitle = document.getElementById('chatTitle');
            const chatSubtitle = document.getElementById('chatSubtitle');
            const displayName = user.nickname || user.username || '未知用户';

            chatTitle.textContent = `与 ${displayName} 的私聊`;
            chatSubtitle.textContent = `用户ID: ${user.id}`;

            // 显示聊天输入框
            document.getElementById('chatInput').style.display = 'block';
            document.getElementById('chatPlaceholder').style.display = 'none';

            // 清空消息区域并加载历史消息
            const messagesDiv = document.getElementById('messages');
            messagesDiv.innerHTML = '';

            // 加载该用户的聊天历史
            const userIdKey = String(user.id);
            const history = chatHistory.get(userIdKey) || [];
            console.log('加载聊天历史，用户ID:', userIdKey, '历史消息数量:', history.length);
            history.forEach(msg => displayMessage(msg));

            // 更新用户列表中的选中状态
            updateUserList(document.getElementById('onlineUsers').userData || []);

            // 聚焦到输入框
            document.getElementById('messageInput').focus();

            console.log('开始与用户私聊:', displayName, user.id);
        }

        // 添加到聊天历史
        function addToChatHistory(userId, messageData) {
            // 确保userId是字符串类型，保持一致性
            const userIdKey = String(userId);

            if (!chatHistory.has(userIdKey)) {
                chatHistory.set(userIdKey, []);
            }
            chatHistory.get(userIdKey).push(messageData);

            // 限制历史消息数量
            const history = chatHistory.get(userIdKey);
            if (history.length > 100) {
                history.splice(0, history.length - 100);
            }

            console.log('消息已添加到聊天历史，用户ID:', userIdKey, '历史消息数量:', history.length);
        }

        // 请求在线用户列表
        function requestOnlineUsers() {
            if (!websocket || websocket.readyState !== WebSocket.OPEN) {
                console.warn('WebSocket连接未就绪，无法请求用户列表');
                return;
            }

            const requestMessage = {
                type: 'get_online_users',
                data: {
                    roomId: 'default' // 默认房间
                },
                timestamp: Date.now()
            };

            console.log('请求在线用户列表');
            websocket.send(JSON.stringify(requestMessage));
        }

        // 显示状态
        function showStatus(message, isError) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status' + (isError ? ' error' : '');
            statusDiv.style.display = 'block';
        }

        // 隐藏状态
        function hideStatus() {
            document.getElementById('status').style.display = 'none';
        }

        // 格式化时间
        function formatTime(timestamp) {
            const date = new Date(timestamp);
            return date.toLocaleTimeString();
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // HTTP请求工具函数 - 自动添加JWT token
        function makeAuthenticatedRequest(url, options = {}) {
            // 确保headers存在
            if (!options.headers) {
                options.headers = {};
            }

            // 添加JWT token到Authorization header
            if (accessToken && tokenType) {
                options.headers['Authorization'] = `${tokenType} ${accessToken}`;
            }

            // 添加Content-Type（如果没有设置）
            if (!options.headers['Content-Type'] && options.method !== 'GET') {
                options.headers['Content-Type'] = 'application/json';
            }

            console.log('发起认证请求:', {
                url: url,
                method: options.method || 'GET',
                hasToken: !!(accessToken && tokenType),
                tokenType: tokenType
            });

            return fetch(url, options)
                .then(response => {
                    // 检查认证状态
                    if (response.status === 401) {
                        console.warn('JWT token已过期或无效，跳转到登录页面');
                        showStatus('登录已过期，请重新登录', true);
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 2000);
                        throw new Error('认证失败，请重新登录');
                    }
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .catch(error => {
                    console.error('HTTP请求失败:', error);
                    throw error;
                });
        }

        // 便捷的GET请求函数
        function apiGet(url) {
            return makeAuthenticatedRequest(url, { method: 'GET' });
        }

        // 便捷的POST请求函数
        function apiPost(url, data) {
            return makeAuthenticatedRequest(url, {
                method: 'POST',
                body: JSON.stringify(data)
            });
        }

        // 便捷的PUT请求函数
        function apiPut(url, data) {
            return makeAuthenticatedRequest(url, {
                method: 'PUT',
                body: JSON.stringify(data)
            });
        }

        // 便捷的DELETE请求函数
        function apiDelete(url) {
            return makeAuthenticatedRequest(url, { method: 'DELETE' });
        }

        // 示例：获取在线用户列表（HTTP方式，作为WebSocket的备用）
        function getOnlineUsersViaHttp() {
            return apiGet('/api/chat/online-users')
                .then(response => {
                    if (response.success && response.data) {
                        console.log('通过HTTP获取在线用户列表成功:', response.data);
                        return response.data;
                    } else {
                        throw new Error(response.message || '获取在线用户列表失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP获取在线用户列表失败:', error);
                    throw error;
                });
        }

        // 示例：获取聊天历史（HTTP方式）
        function getChatHistoryViaHttp(otherUserId, page = 1, size = 50) {
            const currentUserId = getCurrentUserId();
            if (!currentUserId) {
                return Promise.reject(new Error('当前用户ID未知'));
            }

            return apiGet(`/api/chat/users/${currentUserId}/private-history?otherUserId=${otherUserId}&page=${page}&size=${size}`)
                .then(response => {
                    if (response.success && response.data) {
                        console.log('通过HTTP获取聊天历史成功:', response.data.length, '条消息');
                        return response.data;
                    } else {
                        throw new Error(response.message || '获取聊天历史失败');
                    }
                })
                .catch(error => {
                    console.error('通过HTTP获取聊天历史失败:', error);
                    throw error;
                });
        }

        // 测试批量用户信息获取（会触发AuthFeignRequestInterceptor）
        function testBatchUserInfo() {
            console.log('=== 测试批量用户信息获取 ===');
            console.log('当前token状态:', {
                hasAccessToken: !!accessToken,
                tokenType: tokenType,
                tokenLength: accessToken ? accessToken.length : 0
            });

            // 调用chat-service的接口，这会内部调用auth-service的/api/users/batch
            return apiPost('/test/trigger-batch-user-call', {
                userIds: [1, 2, 3]
            })
                .then(response => {
                    console.log('批量用户信息测试成功:', response);
                    return response;
                })
                .catch(error => {
                    console.error('批量用户信息测试失败:', error);
                    throw error;
                });
        }

        // 事件监听
        document.getElementById('sendBtn').addEventListener('click', sendMessage);
        document.getElementById('messageInput').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });

        // 验证JWT token有效性
        function validateTokenOnLoad() {
            if (!accessToken || !tokenType) {
                console.warn('页面加载时未找到JWT token，可能需要重新登录');
                return;
            }

            // 可以选择性地验证token有效性
            console.log('页面加载时JWT token信息:', {
                tokenType: tokenType,
                tokenLength: accessToken.length,
                tokenPreview: accessToken.substring(0, 20) + '...'
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            validateTokenOnLoad();
            initWebSocket();
        });

        // 页面卸载时关闭连接
        window.addEventListener('beforeunload', function () {
            if (websocket) {
                websocket.close();
            }
        });
    </script>
</body>

</html>