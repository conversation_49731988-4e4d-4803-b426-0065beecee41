package com.xiang.chat.jwt;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * Auth服务Feign请求拦截器
 * 自动为调用auth-service的请求添加JWT token
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AuthFeignRequestInterceptor implements RequestInterceptor {

    private final JwtTokenProvider jwtTokenProvider;

    @Override
    public void apply(RequestTemplate template) {
        // 只对auth-service的用户相关接口添加token
        String url = template.url();
        log.debug("=== Feign Request Interceptor ===");
        log.debug("Request URL: {}", url);
        log.debug("Current Thread: {}", Thread.currentThread().getName());
        log.debug("Needs authentication: {}", needsAuthentication(url));

        if (needsAuthentication(url)) {
            String token = getTokenFromMultipleSources();
            log.debug("Token from multiple sources: {}", token != null ? "present (length: " + token.length() + ")" : "null");

            if (token != null && !token.trim().isEmpty()) {
                template.header("Authorization", "Bearer " + token);
                log.info("✅ Added JWT token to Feign request: {} (token length: {})", url, token.length());
            } else {
                log.warn("❌ No JWT token available for authenticated request: {}", url);
                logDetailedTokenDebugInfo();
            }
        } else {
            log.debug("⏭️ Skipping token addition for URL: {}", url);
        }
        log.debug("=== End Feign Request Interceptor ===");
    }

    /**
     * 从多个来源尝试获取token
     */
    private String getTokenFromMultipleSources() {
        // 1. 首先尝试从JwtTokenProvider获取（包括预先提取的Feign token）
        String token = jwtTokenProvider.getCurrentToken();
        if (token != null) {
            log.debug("Got token from JwtTokenProvider (may include pre-extracted Feign token)");
            return token;
        }

        // 2. 尝试从当前请求的RequestContext获取（如果还在HTTP请求线程中）
        try {
            org.springframework.web.context.request.RequestAttributes requestAttributes =
                org.springframework.web.context.request.RequestContextHolder.getRequestAttributes();
            if (requestAttributes instanceof org.springframework.web.context.request.ServletRequestAttributes servletRequestAttributes) {
                String authHeader = servletRequestAttributes.getRequest().getHeader("Authorization");
                if (authHeader != null && authHeader.startsWith("Bearer ")) {
                    token = authHeader.substring(7);
                    log.debug("Got token from RequestContext");
                    return token;
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get token from RequestContext: {}", e.getMessage());
        }

        // 3. 尝试从SecurityContext获取（即使在不同线程中）
        try {
            org.springframework.security.core.Authentication authentication =
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getDetails() instanceof com.xiang.chat.service.OAuth2TokenResponse tokenResponse) {
                token = tokenResponse.getAccessToken();
                if (token != null) {
                    log.debug("Got token from SecurityContext details");
                    return token;
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get token from SecurityContext: {}", e.getMessage());
        }

        log.warn("No token found from any source in thread: {}", Thread.currentThread().getName());
        return null;
    }

    /**
     * 记录详细的token调试信息
     */
    private void logDetailedTokenDebugInfo() {
        try {
            log.debug("=== Detailed Token Debug Info ===");

            // SecurityContext信息
            org.springframework.security.core.Authentication auth =
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            log.debug("SecurityContext Authentication: {}", auth != null ? auth.getClass().getSimpleName() : "null");
            log.debug("Authentication name: {}", auth != null ? auth.getName() : "null");
            log.debug("Authentication details: {}", auth != null && auth.getDetails() != null ? auth.getDetails().getClass().getSimpleName() : "null");

            // RequestContext信息
            org.springframework.web.context.request.RequestAttributes requestAttributes =
                org.springframework.web.context.request.RequestContextHolder.getRequestAttributes();
            log.debug("RequestAttributes: {}", requestAttributes != null ? requestAttributes.getClass().getSimpleName() : "null");

            log.debug("=== End Detailed Token Debug Info ===");
        } catch (Exception e) {
            log.debug("Failed to log debug info: {}", e.getMessage());
        }
    }

    /**
     * 判断请求是否需要认证
     * 
     * @param url 请求URL
     * @return true如果需要认证，否则false
     */
    private boolean needsAuthentication(String url) {
        // OAuth2 token端点使用Basic认证，不需要JWT token
        if (url.startsWith("/oauth2/token") || url.contains("/oauth2/token")) {
            return false;
        }

        // JWT验证接口不需要认证（它本身就是用来验证token的）
        if (url.startsWith("/api/jwt/validate") || url.contains("/api/jwt/validate")) {
            return false;
        }

        // 需要JWT认证的接口
        return url.startsWith("/api/users/") ||
                url.contains("/api/users/") ||
                url.startsWith("/api/jwt/userinfo") ||
                url.contains("/api/jwt/userinfo");
    }
}