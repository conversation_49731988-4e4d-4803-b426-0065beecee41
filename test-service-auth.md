# 服务间认证测试说明

## 测试步骤

### 1. 测试client_credentials模式获取token
```bash
curl -X POST http://localhost:9000/oauth2/token \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Authorization: Basic Y2hhdC1zZXJ2aWNlOmNoYXQtc2VydmljZS1zZWNyZXQ=" \
  -d "grant_type=client_credentials&scope=read write"
```

注意：Authorization头中的值是`chat-service:chat-service-secret`的Base64编码

### 2. 使用获取的token调用用户API
```bash
curl -X GET http://localhost:9000/api/users/3 \
  -H "Authorization: Bearer <access_token>"
```

### 3. 验证chat-service的Feign调用
启动chat-service后，观察日志中是否有自动获取服务token的记录。

## 配置说明

### auth-service配置
- 添加了支持client_credentials模式的客户端
- 客户端ID: `chat-service`
- 客户端密钥: `chat-service-secret`

### chat-service配置
- 添加了服务间认证配置
- 自动获取和缓存服务token
- Feign调用时自动添加JWT token

## 预期行为
1. chat-service启动时会自动获取服务token
2. 调用auth-service的用户API时会自动添加token
3. 如果有用户token则优先使用用户token
4. 没有用户token时使用服务token