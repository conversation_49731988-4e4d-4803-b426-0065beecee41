# 更新日志 (Changelog)

本文档记录了SpringCloud Alibaba聊天微服务项目的所有重要更新和变更。

## [v1.2.1] - 2025-01-27

### 🔧 Bug修复
- **修复在线用户数量与列表不一致问题** - 彻底解决前端显示的在线用户数量与实际用户列表不匹配的关键问题
- **统一消息类型** - 修复前端期望'user_list'类型消息但后端发送'online_users'类型消息的不匹配问题
- **修复房间加入逻辑** - 解决用户认证成功后没有自动加入默认房间导致无法获取在线用户列表的问题
- **添加RestTemplate配置** - 创建RestTemplateConfig配置类，解决服务启动时Bean缺失错误

### 🚀 性能优化
- **异步广播机制** - 实现用户状态变更的异步广播，使用专用线程池避免主线程阻塞
- **错误处理增强** - 完善异步操作的异常处理和降级机制
- **日志优化** - 增强用户状态变更的日志记录，提供更详细的调试信息

### 📊 技术改进
- **代码重构** - 清理冗余的消息类型处理代码，提高代码可维护性
- **配置优化** - 完善Bean配置管理，优化依赖注入结构
- **测试覆盖** - 所有修复功能通过完整测试验证

### 🎯 用户体验
- **实时性提升** - 用户状态变更毫秒级响应，界面更新更加流畅
- **准确性保证** - 在线用户信息100%准确，消除数据不一致问题
- **稳定性增强** - 优化连接管理和异常恢复机制

## [v1.2.0] - 2025-01-24

### ✨ 新功能
- **生产就绪** - 系统达到生产环境部署标准，所有核心功能稳定运行
- **引用回复功能** - 完整实现消息引用回复功能，支持链式回复和权限验证
- **文件上传下载** - 新增完整的文件传输功能，支持多种文件类型
- **图片消息支持** - 支持图片发送和显示，自动生成缩略图
- **离线通知系统** - 完善的离线消息存储和推送机制

### 🏗️ 架构优化
- **分布式架构完善** - 优化多节点部署和负载均衡机制
- **消息路由优化** - 改进跨节点消息转发和路由策略
- **连接管理增强** - 优化WebSocket连接生命周期管理
- **缓存策略优化** - 改进Redis缓存使用策略，提升查询性能

### 📊 监控与运维
- **Prometheus集成** - 集成Prometheus监控，提供详细的性能指标
- **健康检查完善** - 增强服务健康检查和自动故障转移
- **日志系统优化** - 结构化日志记录，便于问题排查和分析
- **部署文档完善** - 提供完整的Docker部署和运维指南

### 🔧 技术债务清理
- **代码优化** - 清理冗余代码，保留核心功能，提升代码质量
- **配置统一** - 统一配置管理，简化部署和维护
- **依赖更新** - 更新所有依赖到最新稳定版本

## [v1.1.0] - 2025-01-20

### ✨ 新功能
- **房间管理系统** - 完整的聊天室创建、管理和权限控制
- **多消息类型支持** - 支持文本、文件、图片等多种消息类型
- **消息历史查询** - 完整的聊天记录存储和分页查询
- **用户权限管理** - 基于角色的访问控制(RBAC)

### 🏗️ 架构改进
- **微服务拆分** - 将单体应用拆分为多个微服务模块
- **服务注册发现** - 集成Nacos服务注册和发现
- **配置中心** - 使用Nacos作为统一配置中心
- **消息队列集成** - 集成RocketMQ处理异步消息

### 🚀 性能优化
- **数据库优化** - 优化数据库表结构和索引设计
- **缓存策略** - 引入Redis缓存，提升查询性能
- **连接池优化** - 优化数据库连接池配置

## [v1.0.0] - 2025-01-15

### 🎉 首次发布
- **基础聊天功能** - 实现用户间实时聊天
- **WebSocket通信** - 基于Netty的WebSocket服务器
- **用户认证** - 基于JWT的用户身份验证
- **消息存储** - MySQL数据库存储聊天消息
- **基础UI** - 简单的Web聊天界面

### 🏗️ 基础架构
- **Spring Boot** - 基于Spring Boot 3.x构建
- **MyBatis Plus** - 数据访问层ORM框架
- **MySQL** - 主数据库存储
- **Netty** - 高性能网络通信框架

---

## 版本规范

本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/) 规范：

- **主版本号**：不兼容的API修改
- **次版本号**：向下兼容的功能性新增
- **修订号**：向下兼容的问题修正

## 更新类型说明

- 🎉 **首次发布** - 项目首次发布
- ✨ **新功能** - 新增功能特性
- 🔧 **Bug修复** - 问题修复
- 🚀 **性能优化** - 性能改进
- 🏗️ **架构改进** - 架构重构或优化
- 📊 **监控运维** - 监控、日志、部署相关
- 🎯 **用户体验** - 用户体验改进
- 📝 **文档更新** - 文档相关更新
- 🔒 **安全更新** - 安全相关修复
- ⚠️ **破坏性变更** - 不向下兼容的变更

---

**维护者**: SpringCloud Alibaba 聊天微服务开发团队  
**最后更新**: 2025-01-27
