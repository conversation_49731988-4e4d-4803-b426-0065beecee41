package com.xiang.chat.jwt;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import java.io.IOException;

/**
 * Feign Token准备过滤器
 * 在HTTP请求开始时预先提取JWT token并存储，供后续的Feign调用使用
 */
@Slf4j
@Component
@Order(1) // 确保在其他过滤器之前执行
@RequiredArgsConstructor
public class FeignTokenPreparationFilter extends OncePerRequestFilter {

    private final JwtTokenProvider jwtTokenProvider;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        try {
            String token = extractTokenFromRequest(request);
            
            if (token != null) {
                // 将token设置到专门的Feign ThreadLocal中
                jwtTokenProvider.setFeignToken(token);
                log.debug("Pre-extracted JWT token for Feign calls, length: {}", token.length());
            } else {
                log.debug("No JWT token found in request for pre-extraction");
            }
            
            // 继续过滤器链
            filterChain.doFilter(request, response);
            
        } finally {
            // 请求结束后清理Feign token
            jwtTokenProvider.clearFeignToken();
        }
    }

    /**
     * 从HTTP请求中提取JWT token
     */
    private String extractTokenFromRequest(HttpServletRequest request) {
        // 1. 首先尝试从Authorization header获取
        String authHeader = request.getHeader("Authorization");
        if (authHeader != null && authHeader.startsWith("Bearer ")) {
            String token = authHeader.substring(7);
            log.debug("Found JWT token in Authorization header");
            return token;
        }
        
        // 2. 尝试从SecurityContext获取
        try {
            Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
            if (authentication != null && authentication.getDetails() instanceof com.xiang.chat.service.OAuth2TokenResponse tokenResponse) {
                String token = tokenResponse.getAccessToken();
                if (token != null) {
                    log.debug("Found JWT token in SecurityContext details");
                    return token;
                }
            }
        } catch (Exception e) {
            log.debug("Failed to get token from SecurityContext: {}", e.getMessage());
        }
        
        return null;
    }
}
