# 聊天页面私聊功能修改说明

## 主要修改内容

### 1. UI界面修改
- **侧边栏标题**：从"聊天室"改为"在线用户"，并添加"点击用户开始私聊"提示
- **聊天标题**：动态显示当前私聊对象，如"与 用户名 的私聊"
- **用户列表**：
  - 当前用户显示为灰色且不可点击，后面显示"(我)"标识
  - 其他用户可点击开始私聊
  - 当前聊天用户高亮显示
- **聊天区域**：默认显示选择用户提示，选择用户后显示聊天界面

### 2. JavaScript功能修改

#### 新增变量
- `currentChatUser`: 当前私聊的用户对象
- `chatHistory`: Map类型，存储每个用户的聊天历史

#### 核心功能函数

**startPrivateChat(user)**
- 开始与指定用户的私聊
- 更新聊天标题和界面
- 加载历史聊天记录
- 显示输入框

**handlePrivateMessage(data)**
- 处理接收到的私聊消息
- 自动添加到聊天历史
- 如果正在与发送者聊天则显示消息
- 否则显示新消息提示

**addToChatHistory(userId, messageData)**
- 将消息添加到指定用户的聊天历史
- 限制历史消息数量（最多100条）

#### 修改的函数

**sendMessage()**
- 改为发送私聊消息（targetType: 'USER'）
- 立即显示自己发送的消息
- 添加到聊天历史

**updateUserList(users)**
- 区分当前用户和其他用户
- 为其他用户添加点击事件
- 保存用户数据供其他函数使用

**displayMessage(data)**
- 支持显示"我"而不是用户名
- 处理isOwn标识

### 3. 消息处理流程

1. **发送消息**：用户输入 → 发送到服务器 → 立即显示在界面 → 添加到历史
2. **接收消息**：服务器推送 → 判断发送者 → 添加到历史 → 如果是当前聊天用户则显示
3. **切换聊天**：点击用户 → 更新界面 → 加载历史消息 → 聚焦输入框

### 4. 用户体验优化

- **消息历史**：每个用户的聊天记录独立保存
- **新消息提示**：非当前聊天用户发送消息时显示提示
- **界面状态**：清晰显示当前聊天对象
- **输入提示**：占位符文本改为"输入私聊消息..."

## 使用说明

1. 页面加载后显示在线用户列表
2. 点击任意其他用户开始私聊
3. 在聊天框中输入消息并发送
4. 可以随时切换到其他用户进行私聊
5. 每个用户的聊天历史会自动保存

## 技术要点

- 使用WebSocket进行实时通信
- 消息类型为'chat_message'，targetType为'USER'
- 支持消息历史本地缓存
- 响应式界面设计
- 错误处理和状态提示