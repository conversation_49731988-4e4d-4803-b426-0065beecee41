package com.xiang.chat.config;

import com.xiang.chat.service.OAuth2AuthService;
import com.xiang.chat.service.OAuth2TokenResponse;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.stereotype.Component;

import java.util.Collections;

@Slf4j
@Component
@RequiredArgsConstructor
public class OAuth2AuthenticationProvider implements AuthenticationProvider {

    private final OAuth2AuthService oAuth2AuthService;

    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        String username = authentication.getName();
        String password = authentication.getCredentials().toString();

        log.debug("Authenticating user: {}", username);

        // 通过auth-service进行OAuth2密码模式认证
        OAuth2TokenResponse tokenResponse = oAuth2AuthService.authenticate(username, password);

        if (tokenResponse.isSuccess()) {
            log.info("User {} authenticated successfully", username);
            log.debug("Access token received: {}", tokenResponse.getAccessToken() != null ? "present" : "null");
            
            // 创建认证成功的Authentication对象
            UsernamePasswordAuthenticationToken authToken = new UsernamePasswordAuthenticationToken(
                    username,
                    null, // 清除密码
                    Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
            );
            
            // 将token信息存储在details中，供后续使用
            authToken.setDetails(tokenResponse);
            log.debug("Token stored in authentication details");
            
            return authToken;
        } else {
            log.warn("Authentication failed for user: {}, error: {}", username, tokenResponse.getError());
            throw new BadCredentialsException("Authentication failed: " + tokenResponse.getErrorDescription());
        }
    }

    @Override
    public boolean supports(Class<?> authentication) {
        return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
    }
}