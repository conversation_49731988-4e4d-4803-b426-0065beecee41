package com.xiang.chat.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.authentication.builders.AuthenticationManagerBuilder;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.oauth2.jwt.JwtDecoder;
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security 安全配置 - 通过OAuth2密码模式认证
 */
@Slf4j
@Configuration
@EnableWebSecurity
@RequiredArgsConstructor
public class SecurityConfig {

    private final OAuth2AuthenticationProvider oAuth2AuthenticationProvider;

    @Value("${spring.security.oauth2.resourceserver.jwt.jwk-set-uri:http://localhost:9000/oauth2/jwks}")
    private String jwkSetUri;

    /**
     * 配置安全过滤器链
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http, AuthenticationManagerBuilder authBuilder)
            throws Exception {
        // 配置自定义认证提供者
        authBuilder.authenticationProvider(oAuth2AuthenticationProvider);

        http
                // 禁用CSRF保护（开发环境）
                .csrf(AbstractHttpConfigurer::disable)

                // 配置请求授权
                .authorizeHttpRequests(authz -> authz
                        // 允许访问登录页面
                        .requestMatchers("/", "/login", "/register", "/api/register").permitAll()

                        // 允许访问静态资源
                        .requestMatchers("/static/**", "/css/**", "/js/**", "/images/**", "/files/**").permitAll()

                        // 允许访问API文档
                        .requestMatchers("/doc.html", "/swagger-ui/**", "/v3/api-docs/**", "/webjars/**").permitAll()

                        // 允许访问健康检查和监控端点
                        .requestMatchers("/actuator/**", "/health", "/info", "/metrics").permitAll()

                        // 允许访问Druid监控
                        .requestMatchers("/druid/**").permitAll()

                        // 允许访问测试接口
                        .requestMatchers("/test/**").permitAll()

                        // 聊天页面需要认证
                        .requestMatchers("/chat").authenticated()

                        // 聊天相关API需要认证
                        .requestMatchers("/api/chat/**").authenticated()

                        // 文件上传接口需要认证
                        .requestMatchers("/api/files/**").authenticated()

                        // 其他请求需要认证
                        .anyRequest().authenticated())

                // 配置OAuth2资源服务器
                .oauth2ResourceServer(oauth2 -> oauth2
                        .jwt(jwt -> jwt.decoder(jwtDecoder())))

                // 配置表单登录
                .formLogin(form -> form
                        .loginPage("/login")
                        .defaultSuccessUrl("/chat", true)
                        .failureUrl("/login?error=true")
                        .permitAll())

                // 配置登出
                .logout(logout -> logout
                        .logoutSuccessUrl("/login?logout=true")
                        .permitAll());

        log.info("Spring Security配置完成 - OAuth2密码模式认证");
        return http.build();
    }

    @Bean
    public JwtDecoder jwtDecoder() {
        return NimbusJwtDecoder.withJwkSetUri(jwkSetUri).build();
    }
}
