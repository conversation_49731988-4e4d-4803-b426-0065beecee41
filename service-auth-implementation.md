# 服务间JWT认证实现说明

## 实现概述
移除了auth-service中`/api/users/**`的白名单配置，改为通过JWT token进行认证。chat-service在调用auth-service时会自动传递JWT token。

## 主要组件

### 1. auth-service修改

#### SecurityConfig.java
```java
// 移除了/api/users/**的白名单
.requestMatchers("/test/**", "/api/jwt/**", "/api/register").permitAll()
```

#### AuthorizationServerConfig.java
```java
// 添加了支持client_credentials模式的服务客户端
RegisteredClient serviceClient = RegisteredClient.withId(UUID.randomUUID().toString())
    .clientId("chat-service")
    .clientSecret("chat-service-secret")
    .authorizationGrantType(AuthorizationGrantType.CLIENT_CREDENTIALS)
    // ...
```

### 2. chat-service新增组件

#### ServiceTokenManager.java
- 管理服务间调用的JWT token
- 支持token缓存和自动刷新
- 使用client_credentials模式获取token

#### JwtTokenProvider.java
- 优先获取用户JWT token
- 如果没有用户token则使用服务token
- 统一的token提供接口

#### AuthFeignRequestInterceptor.java
- Feign请求拦截器
- 自动为auth-service调用添加JWT token
- 只对需要认证的接口添加token

#### AuthFeignConfig.java
- Feign配置类
- 将拦截器应用到AuthFeignService

### 3. 配置文件修改

#### chat-service-dev.yml
```yaml
# 服务间认证配置
service:
  auth:
    enabled: true
    client-id: chat-service
    client-secret: chat-service-secret
```

## 认证流程

### 1. 有用户上下文时
```
用户请求 → chat-service → 获取用户JWT token → 调用auth-service API
```

### 2. 无用户上下文时（如系统内部调用）
```
系统调用 → chat-service → 获取服务JWT token → 调用auth-service API
```

### 3. Token获取流程
```
ServiceTokenManager → client_credentials模式 → auth-service → 获取服务token → 缓存
```

## 安全特性

### 1. Token缓存
- 服务token缓存1小时
- 提前30秒刷新避免过期
- 内存缓存，重启后重新获取

### 2. 认证优先级
1. 用户JWT token（优先）
2. 服务JWT token（备用）
3. 无token（调用失败）

### 3. 权限控制
- 服务token具有read/write权限
- 可以访问所有用户API
- 遵循OAuth2标准

## 错误处理

### 1. Token获取失败
- 记录错误日志
- 返回null，由调用方处理
- 不影响其他功能

### 2. API调用失败
- Feign会抛出相应异常
- 有fallback机制保护
- 错误信息传递给上层

## 监控和调试

### 1. 日志记录
- Token获取成功/失败
- API调用时的token类型
- 认证失败的详细信息

### 2. 调试信息
- 当前安全上下文
- Token有效性检查
- 服务调用链路

## 部署注意事项

### 1. 配置同步
- 确保client-secret在两个服务中一致
- 检查token-uri配置正确

### 2. 启动顺序
- auth-service必须先启动
- chat-service启动时会自动获取token

### 3. 网络连通性
- 确保服务间网络可达
- 检查防火墙和安全组配置