# 测试认证服务的用户API是否可以访问
Write-Host "测试认证服务用户API..."

try {
    # 测试获取单个用户信息
    $response = Invoke-WebRequest -Uri "http://localhost:9000/api/users/3" -Method GET -ErrorAction Stop
    Write-Host "单个用户API测试成功: $($response.StatusCode)"
    Write-Host "响应内容: $($response.Content)"
} catch {
    Write-Host "单个用户API测试失败: $($_.Exception.Message)"
}

Write-Host ""

try {
    # 测试批量获取用户信息
    $body = @{
        userIds = @(1, 2, 3)
    } | ConvertTo-Json
    
    $response = Invoke-WebRequest -Uri "http://localhost:9000/api/users/batch" -Method POST -Body $body -ContentType "application/json" -ErrorAction Stop
    Write-Host "批量用户API测试成功: $($response.StatusCode)"
    Write-Host "响应内容: $($response.Content)"
} catch {
    Write-Host "批量用户API测试失败: $($_.Exception.Message)"
}